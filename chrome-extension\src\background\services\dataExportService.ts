import { createLogger } from '../log';
import type { ExtractedData } from './dataExtractionService';

const logger = createLogger('DataExportService');

export interface ExportOptions {
  format: 'csv' | 'json' | 'excel';
  filename?: string;
  includeMetadata?: boolean;
  customHeaders?: Record<string, string>;
}

export interface ExportResult {
  success: boolean;
  downloadUrl?: string;
  filename: string;
  size: number;
  error?: string;
}

export class DataExportService {
  /**
   * Export extracted data to the specified format
   */
  async exportData(data: ExtractedData, options: ExportOptions): Promise<ExportResult> {
    try {
      logger.info('Starting data export', { format: options.format, itemCount: data.data.length });

      const filename = options.filename || this.generateFilename(data, options.format);
      
      let blob: Blob;
      let mimeType: string;

      switch (options.format) {
        case 'csv':
          blob = await this.exportToCSV(data, options);
          mimeType = 'text/csv';
          break;
        case 'json':
          blob = await this.exportToJSON(data, options);
          mimeType = 'application/json';
          break;
        case 'excel':
          blob = await this.exportToExcel(data, options);
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      // Create download URL
      const downloadUrl = URL.createObjectURL(blob);

      // Trigger download
      await this.triggerDownload(downloadUrl, filename);

      logger.info('Data export completed', { filename, size: blob.size });

      return {
        success: true,
        downloadUrl,
        filename,
        size: blob.size,
      };

    } catch (error) {
      logger.error('Data export failed', error);
      return {
        success: false,
        filename: options.filename || 'export',
        size: 0,
        error: error instanceof Error ? error.message : 'Unknown export error',
      };
    }
  }

  /**
   * Export data to CSV format
   */
  private async exportToCSV(data: ExtractedData, options: ExportOptions): Promise<Blob> {
    try {
      if (data.data.length === 0) {
        throw new Error('No data to export');
      }

      // Get all unique field names
      const fieldNames = new Set<string>();
      data.data.forEach(item => {
        Object.keys(item).forEach(key => fieldNames.add(key));
      });

      const headers = Array.from(fieldNames);
      
      // Apply custom headers if provided
      const displayHeaders = headers.map(header => 
        options.customHeaders?.[header] || this.formatHeader(header)
      );

      // Create CSV content
      const csvRows: string[] = [];
      
      // Add headers
      csvRows.push(displayHeaders.map(header => this.escapeCSVField(header)).join(','));

      // Add data rows
      data.data.forEach(item => {
        const row = headers.map(header => {
          const value = item[header];
          return this.escapeCSVField(this.formatValue(value));
        });
        csvRows.push(row.join(','));
      });

      // Add metadata if requested
      if (options.includeMetadata && data.metadata) {
        csvRows.push(''); // Empty row
        csvRows.push('Metadata');
        csvRows.push(`Source URL,${this.escapeCSVField(data.metadata.source_url)}`);
        csvRows.push(`Extraction Date,${this.escapeCSVField(new Date(data.metadata.extraction_timestamp).toISOString())}`);
        csvRows.push(`Total Items,${data.metadata.total_items}`);
        csvRows.push(`Extraction Method,${this.escapeCSVField(data.metadata.extraction_method)}`);
        csvRows.push(`Confidence Score,${data.metadata.confidence_score}`);
      }

      const csvContent = csvRows.join('\n');
      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    } catch (error) {
      logger.error('CSV export failed', error);
      throw error;
    }
  }

  /**
   * Export data to JSON format
   */
  private async exportToJSON(data: ExtractedData, options: ExportOptions): Promise<Blob> {
    try {
      let exportData: any;

      if (options.includeMetadata) {
        exportData = {
          data: data.data,
          metadata: data.metadata,
          export_info: {
            exported_at: new Date().toISOString(),
            format: 'json',
            total_items: data.data.length,
          },
        };
      } else {
        exportData = data.data;
      }

      const jsonContent = JSON.stringify(exportData, null, 2);
      return new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });

    } catch (error) {
      logger.error('JSON export failed', error);
      throw error;
    }
  }

  /**
   * Export data to Excel format (simplified XLSX)
   */
  private async exportToExcel(data: ExtractedData, options: ExportOptions): Promise<Blob> {
    try {
      // For now, we'll create a simple Excel-compatible CSV with proper formatting
      // In a full implementation, you would use a library like xlsx or exceljs
      
      if (data.data.length === 0) {
        throw new Error('No data to export');
      }

      // Get all unique field names
      const fieldNames = new Set<string>();
      data.data.forEach(item => {
        Object.keys(item).forEach(key => fieldNames.add(key));
      });

      const headers = Array.from(fieldNames);
      
      // Apply custom headers if provided
      const displayHeaders = headers.map(header => 
        options.customHeaders?.[header] || this.formatHeader(header)
      );

      // Create Excel-compatible content
      const rows: string[][] = [];
      
      // Add headers
      rows.push(displayHeaders);

      // Add data rows
      data.data.forEach(item => {
        const row = headers.map(header => {
          const value = item[header];
          return this.formatValue(value);
        });
        rows.push(row);
      });

      // Add metadata sheet if requested
      if (options.includeMetadata && data.metadata) {
        rows.push([]); // Empty row
        rows.push(['Metadata']);
        rows.push(['Source URL', data.metadata.source_url]);
        rows.push(['Extraction Date', new Date(data.metadata.extraction_timestamp).toISOString()]);
        rows.push(['Total Items', data.metadata.total_items.toString()]);
        rows.push(['Extraction Method', data.metadata.extraction_method]);
        rows.push(['Confidence Score', data.metadata.confidence_score.toString()]);
      }

      // Convert to CSV format (Excel can read CSV files)
      const csvContent = rows.map(row => 
        row.map(cell => this.escapeCSVField(cell)).join(',')
      ).join('\n');

      // Add BOM for proper Excel UTF-8 handling
      const bom = '\uFEFF';
      return new Blob([bom + csvContent], { 
        type: 'application/vnd.ms-excel;charset=utf-8;' 
      });

    } catch (error) {
      logger.error('Excel export failed', error);
      throw error;
    }
  }

  /**
   * Generate filename based on data and format
   */
  private generateFilename(data: ExtractedData, format: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    let domain = 'extracted_data';

    try {
      if (data.metadata.source_url && data.metadata.source_url !== 'AI_GENERATED' && data.metadata.source_url !== 'VALIDATOR_RESULT') {
        domain = new URL(data.metadata.source_url).hostname.replace(/[^a-zA-Z0-9]/g, '_');
      } else if (data.metadata.extraction_method) {
        domain = data.metadata.extraction_method.replace(/[^a-zA-Z0-9]/g, '_');
      }
    } catch (error) {
      logger.warn('Failed to parse source URL for filename, using default', {
        sourceUrl: data.metadata.source_url,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    const extension = format === 'excel' ? 'xlsx' : format;
    return `${domain}_${timestamp}.${extension}`;
  }

  /**
   * Format header names for display
   */
  private formatHeader(header: string): string {
    return header
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Format values for export
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }

  /**
   * Escape CSV field values
   */
  private escapeCSVField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  /**
   * Trigger file download
   */
  private async triggerDownload(url: string, filename: string): Promise<void> {
    try {
      // Use Chrome downloads API if available
      if (typeof chrome !== 'undefined' && chrome?.downloads) {
        await new Promise<void>((resolve, reject) => {
          chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true,
          }, (downloadId) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        });
      } else {
        // Fallback: In background script, we can't create DOM elements
        // Instead, we'll send a message to content script or use a different approach
        throw new Error('Chrome downloads API not available');
      }
    } catch (error) {
      logger.error('Download trigger failed', error);
      throw error;
    }
  }
}
